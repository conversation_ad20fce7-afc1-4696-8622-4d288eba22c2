# DataSource Config
spring:
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
  h2:
    console:
      enabled: true
mybatis-plus:
  mapper-locations: classpath:/mapper/*Mapper.xml
  type-aliases-package: com.cospowers.server.*.entity
  log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  configuration:
    # ???????? ?????? org.apache.ibatis.logging.stdout.StdOutImpl
    # ?????? (????? p6spy ??) org.apache.ibatis.logging.nologging.NoLoggingImpl
    # ?????? org.apache.ibatis.logging.slf4j.Slf4jImpl
    logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl
  global-config:
    sql-parser-cache: true
    # ??MP3.0???banner
    banner: false
    db-config:
      # ????
      id-type: ASSIGN_ID
      # ??????(???????? ??????)
      logicDeleteValue: 2
      # ??????
      logicNotDeleteValue: 0
      insertStrategy: NOT_NULL
      updateStrategy: NOT_NULL
      whereStrategy: NOT_NULL
