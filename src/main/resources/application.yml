# DataSource Config
spring:
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: *****************************************************************************************************************************************************************************************************************************
    username: root
    password: root_pro1303
mybatis-plus:
  mapper-locations: classpath:/mapper/*Mapper.xml
  type-aliases-package: com.cospowers.server.*.entity
  log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  configuration:
    # ???????? ?????? org.apache.ibatis.logging.stdout.StdOutImpl
    # ?????? (????? p6spy ??) org.apache.ibatis.logging.nologging.NoLoggingImpl
    # ?????? org.apache.ibatis.logging.slf4j.Slf4jImpl
    logImpl: org.apache.ibatis.logging.nologging.NoLoggingImpl
  global-config:
    sql-parser-cache: true
    # ??MP3.0???banner
    banner: false
    db-config:
      # ????
      id-type: ASSIGN_ID
      # ??????(???????? ??????)
      logicDeleteValue: 2
      # ??????
      logicNotDeleteValue: 0
      insertStrategy: NOT_NULL
      updateStrategy: NOT_NULL
      whereStrategy: NOT_NULL