package com.example.demo.adapter;

import com.example.demo.app.SysUserService;
import com.example.demo.app.dto.Result;
import com.example.demo.app.dto.UserCreateRequest;
import com.example.demo.app.dto.UserStatisticsResponse;
import com.example.demo.infrastructure.data.SysUserDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户管理控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
public class SysUserController {

  @Autowired
  private SysUserService sysUserService;

  /**
   * 根据用户ID获取用户信息
   */
  @GetMapping("/{id}")
  public Result<?> getUserId(@PathVariable("id") String id) {
    return sysUserService.getUserId(id);
  }

  /**
   * 获取用户统计信息（按性别统计）
   */
  @GetMapping("/statistics")
  public Result<UserStatisticsResponse> getUserStatistics() {
    return sysUserService.getUserStatistics();
  }

  /**
   * 查询所有用户
   */
  @GetMapping("/all")
  public Result<List<SysUserDO>> getAllUsers() {
    return sysUserService.getAllUsers();
  }

  /**
   * 按姓名模糊查询用户
   */
  @GetMapping("/search")
  public Result<List<SysUserDO>> getUsersByName(@RequestParam("name") String name) {
    return sysUserService.getUsersByName(name);
  }

  /**
   * 新增用户
   */
  @PostMapping
  public Result<SysUserDO> createUser(@RequestBody UserCreateRequest request) {
    return sysUserService.createUser(request);
  }

  /**
   * 删除用户
   */
  @DeleteMapping("/{id}")
  public Result<String> deleteUser(@PathVariable("id") String id) {
    return sysUserService.deleteUser(id);
  }
}
