package com.example.demo.adapter;


import com.example.demo.app.SysUserService;
import com.example.demo.app.dto.Result;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
public class SysUserController {

    @Resource
    private SysUserService sysUserService;


    @GetMapping("/{id}")
    public Result<?> getUserId(@PathVariable("id") String id) {

        return sysUserService.getUserId(id);
    }


}
