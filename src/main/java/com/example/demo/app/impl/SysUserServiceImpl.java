package com.example.demo.app.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.example.demo.app.SysUserService;
import com.example.demo.app.dto.Result;
import com.example.demo.app.dto.UserCreateRequest;
import com.example.demo.app.dto.UserStatisticsResponse;
import com.example.demo.infrastructure.data.SysUserDO;
import com.example.demo.infrastructure.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.UUID;

@Service
public class SysUserServiceImpl implements SysUserService {

  @Autowired
  private SysUserMapper sysUserMapper;

  @Override
  public Result<?> getUserId(String id) {
    return Result.success(sysUserMapper.selectById(id));
  }

  @Override
  public Result<UserStatisticsResponse> getUserStatistics() {
    try {
      // 统计男性用户数量 (sex = 1)
      QueryWrapper<SysUserDO> maleQuery = new QueryWrapper<>();
      maleQuery.eq("sex", 1);
      Long maleCount = sysUserMapper.selectCount(maleQuery);

      // 统计女性用户数量 (sex = 2)
      QueryWrapper<SysUserDO> femaleQuery = new QueryWrapper<>();
      femaleQuery.eq("sex", 2);
      Long femaleCount = sysUserMapper.selectCount(femaleQuery);

      // 统计总用户数量
      Long totalCount = sysUserMapper.selectCount(null);

      UserStatisticsResponse response = new UserStatisticsResponse(maleCount, femaleCount, totalCount);
      return Result.success(response, "用户统计查询成功");
    } catch (Exception e) {
      return Result.error("用户统计查询失败：" + e.getMessage());
    }
  }

  @Override
  public Result<List<SysUserDO>> getAllUsers() {
    try {
      List<SysUserDO> users = sysUserMapper.selectList(null);
      return Result.success(users, "查询所有用户成功");
    } catch (Exception e) {
      return Result.error("查询所有用户失败：" + e.getMessage());
    }
  }

  @Override
  public Result<List<SysUserDO>> getUsersByName(String name) {
    try {
      if (!StringUtils.hasText(name)) {
        return Result.error("姓名参数不能为空");
      }
      QueryWrapper<SysUserDO> queryWrapper = new QueryWrapper<>();
      queryWrapper.like("name", name);
      List<SysUserDO> users = sysUserMapper.selectList(queryWrapper);
      return Result.success(users, "按姓名模糊查询成功");
    } catch (Exception e) {
      return Result.error("按姓名模糊查询失败：" + e.getMessage());
    }
  }

  @Override
  public Result<SysUserDO> createUser(UserCreateRequest request) {
    try {
      if (request == null || !StringUtils.hasText(request.getName()) || request.getSex() == null) {
        return Result.error("用户信息不完整，姓名和性别不能为空");
      }
      if (request.getSex() != 1 && request.getSex() != 2) {
        return Result.error("性别参数错误，1-男，2-女");
      }

      SysUserDO user = new SysUserDO();
      user.setUserId(UUID.randomUUID().toString().replace("-", ""));
      user.setName(request.getName());
      user.setSex(request.getSex());

      int result = sysUserMapper.insert(user);
      if (result > 0) {
        return Result.success(user, "用户创建成功");
      } else {
        return Result.error("用户创建失败");
      }
    } catch (Exception e) {
      return Result.error("用户创建失败：" + e.getMessage());
    }
  }

  @Override
  public Result<String> deleteUser(String id) {
    try {
      if (!StringUtils.hasText(id)) {
        return Result.error("用户ID不能为空");
      }

      SysUserDO existingUser = sysUserMapper.selectById(id);
      if (existingUser == null) {
        return Result.error("用户不存在");
      }

      int result = sysUserMapper.deleteById(id);
      if (result > 0) {
        return Result.success("删除成功", "用户删除成功");
      } else {
        return Result.error("用户删除失败");
      }
    } catch (Exception e) {
      return Result.error("用户删除失败：" + e.getMessage());
    }
  }
}
