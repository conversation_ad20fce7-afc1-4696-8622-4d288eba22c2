package com.example.demo.app.impl;

import com.example.demo.app.SysUserService;
import com.example.demo.app.dto.Result;
import com.example.demo.infrastructure.mapper.SysUserMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class SysUserServiceImpl implements SysUserService {

    @Resource
    private SysUserMapper sysUserMapper;

    @Override
    public Result<?> getUserId(String id) {
        return Result.success(sysUserMapper.selectById(id));
    }

}
