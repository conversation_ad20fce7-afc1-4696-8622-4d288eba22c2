package com.example.demo.app;

import com.example.demo.app.dto.Result;
import com.example.demo.app.dto.UserCreateRequest;
import com.example.demo.app.dto.UserStatisticsResponse;
import com.example.demo.infrastructure.data.SysUserDO;

import java.util.List;

public interface SysUserService {

  /**
   * 根据用户id 获取对象
   *
   * @param id
   * @return
   */
  Result<?> getUserId(String id);

  /**
   * 获取用户统计信息（按性别统计）
   *
   * @return 用户统计信息
   */
  Result<UserStatisticsResponse> getUserStatistics();

  /**
   * 查询所有用户
   *
   * @return 所有用户列表
   */
  Result<List<SysUserDO>> getAllUsers();

  /**
   * 按姓名模糊查询用户
   *
   * @param name 姓名关键字
   * @return 匹配的用户列表
   */
  Result<List<SysUserDO>> getUsersByName(String name);

  /**
   * 新增用户
   *
   * @param request 用户创建请求
   * @return 创建结果
   */
  Result<SysUserDO> createUser(UserCreateRequest request);

  /**
   * 删除用户
   *
   * @param id 用户ID
   * @return 删除结果
   */
  Result<String> deleteUser(String id);
}
