package com.example.demo.app.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @param <T>
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class Result<T> implements Serializable {

    private Long timestamp = System.currentTimeMillis();

    private int code;

    private T data;

    private String msg;

    private Boolean successed;


    public static <T> Result<T> success(T data) {
        Result<T> rs = new Result<>();
        rs.code = 200;
        rs.data = data;

        rs.msg = "成功";
        rs.successed = true;

        return rs;
    }
}
